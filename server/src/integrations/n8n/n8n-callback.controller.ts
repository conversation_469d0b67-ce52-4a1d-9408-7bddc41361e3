import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  Logger,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { N8nAuthGuard } from './guards/n8n-auth.guard';
import { ContractExtractionCallbackDto, ProposalCallbackDto } from './dto/n8n-callback.dto';
import { PrismaService } from '../../prisma.service';
import { RentalAdvanceStatus } from '../../rental-advance/enums/rental-status.enum';
import { Public } from '../../common/decorators/public.decorator';

@Controller('webhooks/n8n-callback')
@UseGuards(N8nAuthGuard)
@Public()
export class N8nCallbackController {
  private readonly logger = new Logger(N8nCallbackController.name);

  constructor(private readonly prisma: PrismaService) {}

  @Post('contract-extraction')
  @HttpCode(HttpStatus.OK)
  async handleContractExtraction(@Body() payload: ContractExtractionCallbackDto) {
    try {
      // Find the operation
      const operation = await this.prisma.rentalAdvanceRequest.findUnique({
        where: { id: payload.operationId },
        include: { contractData: true },
      });

      if (!operation) {
        this.logger.error(`Operation not found: ${payload.operationId}`);
        throw new NotFoundException('Operation not found');
      }

      if (payload.success && payload.extractedData) {
        // Success: Update with extracted data
        await this.handleExtractionSuccess(payload.operationId, payload.extractedData);
        this.logger.log(`Contract extraction completed successfully for operation: ${payload.operationId}`);
      } else {
        // Failure: Update status to failed
        await this.handleExtractionFailure(payload.operationId, payload.error || 'Unknown extraction error');
        this.logger.error(`Contract extraction failed for operation: ${payload.operationId}, error: ${payload.error}`);
      }

      return { success: true, message: 'Callback processed successfully' };
    } catch (error) {
      this.logger.error(`Error processing contract extraction callback: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to process callback');
    }
  }

  @Post('proposal')
  @HttpCode(HttpStatus.OK)
  async handleProposal(@Body() payload: ProposalCallbackDto) {
    this.logger.log(`Received proposal callback for operation: ${payload.operationId}`);

    try {
      // Find the operation
      const operation = await this.prisma.rentalAdvanceRequest.findUnique({
        where: { id: payload.operationId },
      });

      if (!operation) {
        this.logger.error(`Operation not found: ${payload.operationId}`);
        throw new NotFoundException('Operation not found');
      }

      if (payload.success && payload.proposalData) {
        // Success: Update with proposal data
        await this.handleProposalSuccess(payload.operationId, payload.proposalData);
        this.logger.log(`Proposal generation completed successfully for operation: ${payload.operationId}`);
      } else {
        // Failure: Update status to failed (revert to previous state)
        await this.handleProposalFailure(payload.operationId, payload.error || 'Unknown proposal error');
        this.logger.error(`Proposal generation failed for operation: ${payload.operationId}, error: ${payload.error}`);
      }

      return { success: true, message: 'Callback processed successfully' };
    } catch (error) {
      this.logger.error(`Error processing proposal callback: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to process callback');
    }
  }

  private async handleExtractionSuccess(operationId: string, data: any): Promise<void> {
    const parseDate = (dateString: string) => {
      if (!dateString || typeof dateString !== 'string') return null;
      const parts = dateString.split('/');
      if (parts.length === 3) {
        const [day, month, year] = parts.map(Number);
        // O mês no construtor do Date é 0-indexed (0-11)
        return new Date(year, month - 1, day);
      }
      return null;
    };

    // Helper function to sanitize strings by removing null bytes and other invalid characters
    const sanitizeString = (str: string | null | undefined): string | null => {
      if (!str || typeof str !== 'string') return null;
      
      // Remove null bytes and other control characters except newlines and tabs
      return str
        .replace(/\0/g, '') // Remove null bytes
        .replace(/[\x00-\x08\x0B-\x0C\x0E-\x1F\x7F]/g, '') // Remove other control characters
        .trim();
    };

    const extractedData = {
      propertyAddress: sanitizeString(data.imovel_endereco_completo),
      landlordName: sanitizeString(data.locador_nome),
      tenantName: sanitizeString(data.locatario_nome),
      landlordDocument: sanitizeString(data.locador_cpf_cnpj?.replace(/[^0-9]/g, '')),
      tenantDocument: sanitizeString(data.locatario_cpf_cnpj?.replace(/[^0-9]/g, '')),
      rentalGuarantee: sanitizeString(data.garantia_modalidade),
      propertyRegistry: sanitizeString(data.matricula_imovel),
      startDate: parseDate(data.data_inicio_contrato),
      endDate: parseDate(data.data_fim_contrato),
    };

    // Filter out null values to avoid inserting empty strings
    const toInsert = Object.fromEntries(
      Object.entries(extractedData).filter(([_, value]) => value !== null && value !== '')
    );
    
    try {
      // Create or update contract data
      const response = await this.prisma.rentalContractData.upsert({
        where: { rentalRequestId: operationId },
        create: {
          rentalRequestId: operationId,
          ...toInsert,
        },
        update: {
          ...toInsert,
        },
      });

      this.logger.log(`Contract data saved successfully for operation: ${operationId}`);

      // Update operation status
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operationId },
        data: { currentStatus: RentalAdvanceStatus.PDF_EXTRACTED },
      });

      // Add status log
      await this.addStatusLog(operationId, RentalAdvanceStatus.PDF_EXTRACTED);
    } catch (error) {
      this.logger.error(`Failed to save contract data for operation ${operationId}:`, error);
      
      // If data insertion fails, mark extraction as failed
      await this.handleExtractionFailure(operationId, `Data sanitization failed: ${error.message}`);
      throw error;
    }
  }

  private async handleExtractionFailure(operationId: string, error: string): Promise<void> {
    // Update operation status to failed
    await this.prisma.rentalAdvanceRequest.update({
      where: { id: operationId },
      data: { currentStatus: RentalAdvanceStatus.EXTRACTION_FAILED },
    });

    // Add status log with error
    await this.addStatusLog(operationId, RentalAdvanceStatus.EXTRACTION_FAILED, { error });
  }

  private async handleProposalSuccess(operationId: string, data: any): Promise<void> {
    // Proposal data can be null if it wasn't approved
    if (!data.proposalAmount || !data.proposedMonths) {
      // In this case, we want to mark the proposal as sent but with -1 in all fields, so we can show the error to the user
      data.proposalAmount = -1;
      data.proposedMonths = -1;
    }

    // Update operation with proposal data
    await this.prisma.rentalAdvanceRequest.update({
      where: { id: operationId },
      data: {
        currentStatus: RentalAdvanceStatus.PROPOSAL_SENT,
        proposalAmount: data.proposalAmount ? parseFloat(data.proposalAmount.toString()) : null,
        proposedMonths: data.proposedMonths ? parseInt(data.proposedMonths.toString()) : null,
      },
    });

    // Add status log
    await this.addStatusLog(operationId, RentalAdvanceStatus.PROPOSAL_SENT);
  }

  private async handleProposalFailure(operationId: string, error: string): Promise<void> {
    // Revert to DATA_CONFIRMED status so user can retry
    await this.prisma.rentalAdvanceRequest.update({
      where: { id: operationId },
      data: { currentStatus: RentalAdvanceStatus.DATA_CONFIRMED },
    });

    // Add status log with error
    await this.addStatusLog(operationId, RentalAdvanceStatus.DATA_CONFIRMED, { 
      error: `Proposal generation failed: ${error}` 
    });
  }

  private async addStatusLog(
    operationId: string,
    status: RentalAdvanceStatus,
    additionalData?: any,
  ): Promise<void> {
    await this.prisma.rentalRequestStatusLog.create({
      data: {
        rentalRequestId: operationId,
        status,
      },
    });
  }
}
